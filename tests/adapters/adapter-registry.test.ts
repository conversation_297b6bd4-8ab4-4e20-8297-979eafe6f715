import { describe, it, expect, vi } from 'vitest';

// Mock the problematic got-scraping dependency
vi.mock('got-scraping', () => ({
  gotScraping: vi.fn(),
}));

// Mock playwright
vi.mock('playwright', () => ({
  chromium: {
    launch: vi.fn(),
  },
}));

import { getAdapter, PRIMARY_ADAPTER_NAME, FALLBACK_ADAPTER_NAME } from '../../server/src/adapters/index';

describe('Adapter Registry', () => {
  describe('getAdapter', () => {
    it('should return the primary adapter when requested', () => {
      const adapter = getAdapter(PRIMARY_ADAPTER_NAME);
      expect(adapter).toBeDefined();
      expect(adapter?.name).toBe(PRIMARY_ADAPTER_NAME);
    });

    it('should return the fallback adapter when requested', () => {
      const adapter = getAdapter(FALLBACK_ADAPTER_NAME);
      expect(adapter).toBeDefined();
      expect(adapter?.name).toBe(FALLBACK_ADAPTER_NAME);
    });

    it('should return undefined for non-existent adapter', () => {
      const adapter = getAdapter('non-existent-adapter');
      expect(adapter).toBeUndefined();
    });

    it('should return undefined for empty string', () => {
      const adapter = getAdapter('');
      expect(adapter).toBeUndefined();
    });

    it('should return undefined for null/undefined input', () => {
      const adapter1 = getAdapter(null as any);
      const adapter2 = getAdapter(undefined as any);
      expect(adapter1).toBeUndefined();
      expect(adapter2).toBeUndefined();
    });
  });

  describe('Adapter Configuration', () => {
    it('should have valid primary adapter name', () => {
      expect(PRIMARY_ADAPTER_NAME).toBeDefined();
      expect(typeof PRIMARY_ADAPTER_NAME).toBe('string');
      expect(PRIMARY_ADAPTER_NAME.length).toBeGreaterThan(0);
    });

    it('should have valid fallback adapter name', () => {
      expect(FALLBACK_ADAPTER_NAME).toBeDefined();
      expect(typeof FALLBACK_ADAPTER_NAME).toBe('string');
      expect(FALLBACK_ADAPTER_NAME.length).toBeGreaterThan(0);
    });

    it('should have different primary and fallback adapters', () => {
      expect(PRIMARY_ADAPTER_NAME).not.toBe(FALLBACK_ADAPTER_NAME);
    });

    it('should have both adapters available in registry', () => {
      const primaryAdapter = getAdapter(PRIMARY_ADAPTER_NAME);
      const fallbackAdapter = getAdapter(FALLBACK_ADAPTER_NAME);
      
      expect(primaryAdapter).toBeDefined();
      expect(fallbackAdapter).toBeDefined();
    });
  });

  describe('Adapter Interface', () => {
    it('should ensure primary adapter has correct interface', () => {
      const adapter = getAdapter(PRIMARY_ADAPTER_NAME);
      expect(adapter).toBeDefined();
      expect(adapter?.name).toBe(PRIMARY_ADAPTER_NAME);
      expect(typeof adapter?.scrape).toBe('function');
    });

    it('should ensure fallback adapter has correct interface', () => {
      const adapter = getAdapter(FALLBACK_ADAPTER_NAME);
      expect(adapter).toBeDefined();
      expect(adapter?.name).toBe(FALLBACK_ADAPTER_NAME);
      expect(typeof adapter?.scrape).toBe('function');
    });
  });
});
