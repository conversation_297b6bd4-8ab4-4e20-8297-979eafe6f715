import { describe, it, expect } from 'vitest';
import { cn } from '../../client/src/lib/utils';

describe('Utils', () => {
  describe('cn (className utility)', () => {
    it('should merge class names correctly', () => {
      const result = cn('px-4', 'py-2', 'bg-blue-500');
      expect(result).toBe('px-4 py-2 bg-blue-500');
    });

    it('should handle conditional classes', () => {
      const isActive = true;
      const result = cn('base-class', isActive && 'active-class');
      expect(result).toBe('base-class active-class');
    });

    it('should handle false conditional classes', () => {
      const isActive = false;
      const result = cn('base-class', isActive && 'active-class');
      expect(result).toBe('base-class');
    });

    it('should merge conflicting Tailwind classes correctly', () => {
      const result = cn('px-4', 'px-6'); // px-6 should override px-4
      expect(result).toBe('px-6');
    });

    it('should handle arrays of classes', () => {
      const result = cn(['px-4', 'py-2'], 'bg-blue-500');
      expect(result).toBe('px-4 py-2 bg-blue-500');
    });

    it('should handle objects with conditional classes', () => {
      const result = cn({
        'px-4': true,
        'py-2': true,
        'bg-red-500': false,
        'bg-blue-500': true,
      });
      expect(result).toBe('px-4 py-2 bg-blue-500');
    });

    it('should handle empty inputs', () => {
      expect(cn()).toBe('');
      expect(cn('')).toBe('');
      expect(cn(null)).toBe('');
      expect(cn(undefined)).toBe('');
    });

    it('should handle mixed input types', () => {
      const result = cn(
        'base',
        ['array-class'],
        { 'object-class': true, 'false-class': false },
        'string-class',
        null,
        undefined
      );
      expect(result).toBe('base array-class object-class string-class');
    });

    it('should handle complex Tailwind merge scenarios', () => {
      // Test that conflicting margin classes are properly merged
      const result = cn('m-4', 'mx-6', 'my-8');
      expect(result).toBe('m-4 mx-6 my-8');
    });

    it('should handle responsive classes', () => {
      const result = cn('px-4', 'md:px-6', 'lg:px-8');
      expect(result).toBe('px-4 md:px-6 lg:px-8');
    });
  });
});
