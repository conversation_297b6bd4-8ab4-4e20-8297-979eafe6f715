import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { Request, Response } from 'express';

// Mock the storage module
const mockStorage = {
  getAllScrapers: vi.fn(),
  getScraper: vi.fn(),
  createScraper: vi.fn(),
  updateScraper: vi.fn(),
  deleteScraper: vi.fn(),
  getPriceHistory: vi.fn(),
};

vi.mock('../../server/storage', () => ({
  storage: mockStorage,
}));

// Mock the schema validation
vi.mock('@shared/schema', () => ({
  insertScraperSchema: {
    parse: vi.fn(),
  },
  updateScraperSchema: {
    parse: vi.fn(),
  },
}));

// Import after mocking
import { insertScraperSchema, updateScraperSchema } from '@shared/schema';

describe('API Routes', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockJson: ReturnType<typeof vi.fn>;
  let mockStatus: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockJson = vi.fn();
    mockStatus = vi.fn().mockReturnValue({ json: mockJson });
    
    mockReq = {
      params: {},
      body: {},
    };
    
    mockRes = {
      json: mockJson,
      status: mockStatus,
    };

    // Reset all mocks
    vi.clearAllMocks();
  });

  describe('GET /api/scrapers', () => {
    it('should return all scrapers successfully', async () => {
      const mockScrapers = [
        {
          id: '1',
          itemName: 'Test Product',
          url: 'https://example.com',
          selector: '.price',
          currentPrice: '99.99',
          lowestPrice: '89.99',
          averagePrice: '94.99',
          status: 'active',
          lastUpdated: new Date(),
          lastError: null,
          createdAt: new Date(),
        },
      ];

      mockStorage.getAllScrapers.mockResolvedValue(mockScrapers);

      // Simulate the route handler logic
      try {
        const scrapers = await mockStorage.getAllScrapers();
        mockRes.json!(scrapers);
      } catch (error) {
        mockRes.status!(500).json({ message: 'Failed to fetch scrapers' });
      }

      expect(mockStorage.getAllScrapers).toHaveBeenCalledOnce();
      expect(mockJson).toHaveBeenCalledWith(mockScrapers);
      expect(mockStatus).not.toHaveBeenCalled();
    });

    it('should handle errors when fetching scrapers', async () => {
      mockStorage.getAllScrapers.mockRejectedValue(new Error('Database error'));

      // Simulate the route handler logic
      try {
        await mockStorage.getAllScrapers();
        mockRes.json!([]);
      } catch (error) {
        mockRes.status!(500).json({ message: 'Failed to fetch scrapers' });
      }

      expect(mockStorage.getAllScrapers).toHaveBeenCalledOnce();
      expect(mockStatus).toHaveBeenCalledWith(500);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Failed to fetch scrapers' });
    });
  });

  describe('GET /api/scrapers/:id', () => {
    it('should return a specific scraper', async () => {
      const mockScraper = {
        id: '1',
        itemName: 'Test Product',
        url: 'https://example.com',
        selector: '.price',
        currentPrice: '99.99',
        lowestPrice: '89.99',
        averagePrice: '94.99',
        status: 'active',
        lastUpdated: new Date(),
        lastError: null,
        createdAt: new Date(),
      };

      mockReq.params = { id: '1' };
      mockStorage.getScraper.mockResolvedValue(mockScraper);

      // Simulate the route handler logic
      try {
        const scraper = await mockStorage.getScraper(mockReq.params!.id);
        if (!scraper) {
          mockRes.status!(404).json({ message: 'Scraper not found' });
        } else {
          mockRes.json!(scraper);
        }
      } catch (error) {
        mockRes.status!(500).json({ message: 'Failed to fetch scraper' });
      }

      expect(mockStorage.getScraper).toHaveBeenCalledWith('1');
      expect(mockJson).toHaveBeenCalledWith(mockScraper);
    });

    it('should return 404 for non-existent scraper', async () => {
      mockReq.params = { id: 'non-existent' };
      mockStorage.getScraper.mockResolvedValue(undefined);

      // Simulate the route handler logic
      try {
        const scraper = await mockStorage.getScraper(mockReq.params!.id);
        if (!scraper) {
          mockRes.status!(404).json({ message: 'Scraper not found' });
        } else {
          mockRes.json!(scraper);
        }
      } catch (error) {
        mockRes.status!(500).json({ message: 'Failed to fetch scraper' });
      }

      expect(mockStorage.getScraper).toHaveBeenCalledWith('non-existent');
      expect(mockStatus).toHaveBeenCalledWith(404);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Scraper not found' });
    });
  });

  describe('POST /api/scrapers', () => {
    it('should create a new scraper successfully', async () => {
      const scraperData = {
        itemName: 'New Product',
        url: 'https://example.com/new',
        selector: '.price',
      };

      const createdScraper = {
        id: 'new-id',
        ...scraperData,
        currentPrice: null,
        lowestPrice: null,
        averagePrice: null,
        status: 'active',
        lastUpdated: new Date(),
        lastError: null,
        createdAt: new Date(),
      };

      mockReq.body = scraperData;
      (insertScraperSchema.parse as any).mockReturnValue(scraperData);
      mockStorage.createScraper.mockResolvedValue(createdScraper);

      // Simulate the route handler logic
      try {
        const validatedData = insertScraperSchema.parse(mockReq.body);
        const scraper = await mockStorage.createScraper(validatedData);
        mockRes.status!(201).json(scraper);
      } catch (error) {
        mockRes.status!(500).json({ message: 'Failed to create scraper' });
      }

      expect(insertScraperSchema.parse).toHaveBeenCalledWith(scraperData);
      expect(mockStorage.createScraper).toHaveBeenCalledWith(scraperData);
      expect(mockStatus).toHaveBeenCalledWith(201);
      expect(mockJson).toHaveBeenCalledWith(createdScraper);
    });

    it('should handle validation errors', async () => {
      const invalidData = {
        itemName: '',
        url: 'invalid-url',
        selector: '',
      };

      mockReq.body = invalidData;
      const validationError = new Error('Validation failed');
      validationError.name = 'ZodError';
      (validationError as any).errors = [{ message: 'Invalid data' }];
      (insertScraperSchema.parse as any).mockImplementation(() => {
        throw validationError;
      });

      // Simulate the route handler logic
      try {
        insertScraperSchema.parse(mockReq.body);
      } catch (error: any) {
        if (error.name === 'ZodError') {
          mockRes.status!(400).json({ message: 'Invalid scraper data', errors: error.errors });
        } else {
          mockRes.status!(500).json({ message: 'Failed to create scraper' });
        }
      }

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({
        message: 'Invalid scraper data',
        errors: [{ message: 'Invalid data' }],
      });
    });
  });

  describe('DELETE /api/scrapers/:id', () => {
    it('should delete a scraper successfully', async () => {
      mockReq.params = { id: '1' };
      mockStorage.deleteScraper.mockResolvedValue(true);

      // Simulate the route handler logic
      try {
        const deleted = await mockStorage.deleteScraper(mockReq.params!.id);
        if (!deleted) {
          mockRes.status!(404).json({ message: 'Scraper not found' });
        } else {
          mockRes.json!({ message: 'Scraper deleted successfully' });
        }
      } catch (error) {
        mockRes.status!(500).json({ message: 'Failed to delete scraper' });
      }

      expect(mockStorage.deleteScraper).toHaveBeenCalledWith('1');
      expect(mockJson).toHaveBeenCalledWith({ message: 'Scraper deleted successfully' });
    });

    it('should return 404 when deleting non-existent scraper', async () => {
      mockReq.params = { id: 'non-existent' };
      mockStorage.deleteScraper.mockResolvedValue(false);

      // Simulate the route handler logic
      try {
        const deleted = await mockStorage.deleteScraper(mockReq.params!.id);
        if (!deleted) {
          mockRes.status!(404).json({ message: 'Scraper not found' });
        } else {
          mockRes.json!({ message: 'Scraper deleted successfully' });
        }
      } catch (error) {
        mockRes.status!(500).json({ message: 'Failed to delete scraper' });
      }

      expect(mockStorage.deleteScraper).toHaveBeenCalledWith('non-existent');
      expect(mockStatus).toHaveBeenCalledWith(404);
      expect(mockJson).toHaveBeenCalledWith({ message: 'Scraper not found' });
    });
  });
});
