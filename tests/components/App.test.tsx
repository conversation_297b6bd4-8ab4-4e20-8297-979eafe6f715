import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import React from 'react';
import App from '../../client/src/App';

// Mock the router and query client dependencies
vi.mock('wouter', () => ({
  Switch: ({ children }: { children: React.ReactNode }) => <div data-testid="switch">{children}</div>,
  Route: ({ component: Component }: { component: React.ComponentType }) => <Component />,
}));

vi.mock('@tanstack/react-query', () => ({
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="query-provider">{children}</div>
  ),
}));

vi.mock('@/components/ui/toaster', () => ({
  Toaster: () => <div data-testid="toaster" />,
}));

vi.mock('@/components/ui/tooltip', () => ({
  TooltipProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="tooltip-provider">{children}</div>
  ),
}));

vi.mock('@/pages/dashboard', () => ({
  default: () => <div data-testid="dashboard">Dashboard Page</div>,
}));

vi.mock('@/pages/not-found', () => ({
  default: () => <div data-testid="not-found">Not Found Page</div>,
}));

vi.mock('@/lib/queryClient', () => ({
  queryClient: {},
}));

describe('App Component', () => {
  it('should render without crashing', () => {
    render(<App />);
    
    // Check that the main structure is rendered
    expect(screen.getByTestId('query-provider')).toBeInTheDocument();
    expect(screen.getByTestId('tooltip-provider')).toBeInTheDocument();
    expect(screen.getByTestId('toaster')).toBeInTheDocument();
    expect(screen.getByTestId('switch')).toBeInTheDocument();
  });

  it('should have dark theme class', () => {
    render(<App />);
    
    const darkDiv = document.querySelector('.dark');
    expect(darkDiv).toBeInTheDocument();
  });

  it('should render the router structure', () => {
    render(<App />);
    
    // The Switch component should be present
    expect(screen.getByTestId('switch')).toBeInTheDocument();
  });

  it('should include all necessary providers', () => {
    render(<App />);
    
    // Check that all providers are present
    expect(screen.getByTestId('query-provider')).toBeInTheDocument();
    expect(screen.getByTestId('tooltip-provider')).toBeInTheDocument();
    expect(screen.getByTestId('toaster')).toBeInTheDocument();
  });
});
