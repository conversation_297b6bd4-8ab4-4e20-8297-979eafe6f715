import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { IStorage } from '../../server/storage';
import type { Scraper, InsertScraper, UpdateScraper, PriceHistory } from '@shared/schema';

// Mock storage implementation for testing
class MockStorage implements IStorage {
  private scrapers: Scraper[] = [];
  private priceHistory: PriceHistory[] = [];

  async getScraper(id: string): Promise<Scraper | undefined> {
    return this.scrapers.find(scraper => scraper.id === id);
  }

  async getAllScrapers(): Promise<Scraper[]> {
    return [...this.scrapers];
  }

  async createScraper(scraper: InsertScraper): Promise<Scraper> {
    const newScraper: Scraper = {
      id: `test-${Date.now()}`,
      ...scraper,
      currentPrice: null,
      lowestPrice: null,
      averagePrice: null,
      status: 'active',
      lastUpdated: new Date(),
      lastError: null,
      createdAt: new Date(),
    };
    this.scrapers.push(newScraper);
    return newScraper;
  }

  async updateScraper(id: string, updates: UpdateScraper): Promise<Scraper | undefined> {
    const index = this.scrapers.findIndex(scraper => scraper.id === id);
    if (index === -1) return undefined;

    this.scrapers[index] = {
      ...this.scrapers[index],
      ...updates,
      lastUpdated: new Date(),
    };
    return this.scrapers[index];
  }

  async deleteScraper(id: string): Promise<boolean> {
    const index = this.scrapers.findIndex(scraper => scraper.id === id);
    if (index === -1) return false;

    this.scrapers.splice(index, 1);
    // Also remove related price history
    this.priceHistory = this.priceHistory.filter(entry => entry.scraperId !== id);
    return true;
  }

  async addPriceHistory(scraperId: string, price: string): Promise<void> {
    // Add a small delay to ensure different timestamps
    await new Promise(resolve => setTimeout(resolve, 1));
    const entry: PriceHistory = {
      id: `price-${Date.now()}-${Math.random()}`,
      scraperId,
      price,
      timestamp: new Date(),
    };
    this.priceHistory.push(entry);
  }

  async getPriceHistory(scraperId: string): Promise<PriceHistory[]> {
    return this.priceHistory
      .filter(entry => entry.scraperId === scraperId)
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  async updateAveragePrice(scraperId: string): Promise<void> {
    // Mock implementation - in real tests this would calculate the average
    const scraper = await this.getScraper(scraperId);
    if (scraper) {
      await this.updateScraper(scraperId, { averagePrice: '100.00' });
    }
  }

  // Helper methods for testing
  clear() {
    this.scrapers = [];
    this.priceHistory = [];
  }
}

describe('Storage Interface', () => {
  let storage: MockStorage;

  beforeEach(() => {
    storage = new MockStorage();
  });

  describe('Scraper Operations', () => {
    it('should create a new scraper', async () => {
      const scraperData: InsertScraper = {
        itemName: 'Test Product',
        url: 'https://example.com/product',
        selector: '.price',
      };

      const scraper = await storage.createScraper(scraperData);

      expect(scraper.id).toBeDefined();
      expect(scraper.itemName).toBe(scraperData.itemName);
      expect(scraper.url).toBe(scraperData.url);
      expect(scraper.selector).toBe(scraperData.selector);
      expect(scraper.status).toBe('active');
      expect(scraper.currentPrice).toBe(null);
      expect(scraper.lowestPrice).toBe(null);
      expect(scraper.averagePrice).toBe(null);
    });

    it('should get a scraper by id', async () => {
      const scraperData: InsertScraper = {
        itemName: 'Test Product',
        url: 'https://example.com/product',
        selector: '.price',
      };

      const createdScraper = await storage.createScraper(scraperData);
      const retrievedScraper = await storage.getScraper(createdScraper.id);

      expect(retrievedScraper).toEqual(createdScraper);
    });

    it('should return undefined for non-existent scraper', async () => {
      const scraper = await storage.getScraper('non-existent-id');
      expect(scraper).toBeUndefined();
    });

    it('should get all scrapers', async () => {
      const scraperData1: InsertScraper = {
        itemName: 'Product 1',
        url: 'https://example.com/product1',
        selector: '.price',
      };

      const scraperData2: InsertScraper = {
        itemName: 'Product 2',
        url: 'https://example.com/product2',
        selector: '.price',
      };

      await storage.createScraper(scraperData1);
      await storage.createScraper(scraperData2);

      const scrapers = await storage.getAllScrapers();
      expect(scrapers).toHaveLength(2);
    });

    it('should update a scraper', async () => {
      const scraperData: InsertScraper = {
        itemName: 'Test Product',
        url: 'https://example.com/product',
        selector: '.price',
      };

      const scraper = await storage.createScraper(scraperData);
      const updates: UpdateScraper = {
        itemName: 'Updated Product',
        currentPrice: '99.99',
      };

      const updatedScraper = await storage.updateScraper(scraper.id, updates);

      expect(updatedScraper?.itemName).toBe('Updated Product');
      expect(updatedScraper?.currentPrice).toBe('99.99');
      expect(updatedScraper?.url).toBe(scraperData.url); // Should remain unchanged
    });

    it('should return undefined when updating non-existent scraper', async () => {
      const result = await storage.updateScraper('non-existent-id', { itemName: 'Updated' });
      expect(result).toBeUndefined();
    });

    it('should delete a scraper', async () => {
      const scraperData: InsertScraper = {
        itemName: 'Test Product',
        url: 'https://example.com/product',
        selector: '.price',
      };

      const scraper = await storage.createScraper(scraperData);
      const deleted = await storage.deleteScraper(scraper.id);

      expect(deleted).toBe(true);

      const retrievedScraper = await storage.getScraper(scraper.id);
      expect(retrievedScraper).toBeUndefined();
    });

    it('should return false when deleting non-existent scraper', async () => {
      const deleted = await storage.deleteScraper('non-existent-id');
      expect(deleted).toBe(false);
    });
  });

  describe('Price History Operations', () => {
    let scraperId: string;

    beforeEach(async () => {
      const scraperData: InsertScraper = {
        itemName: 'Test Product',
        url: 'https://example.com/product',
        selector: '.price',
      };
      const scraper = await storage.createScraper(scraperData);
      scraperId = scraper.id;
    });

    it('should add price history entry', async () => {
      await storage.addPriceHistory(scraperId, '99.99');

      const history = await storage.getPriceHistory(scraperId);
      expect(history).toHaveLength(1);
      expect(history[0].price).toBe('99.99');
      expect(history[0].scraperId).toBe(scraperId);
    });

    it('should get price history for a scraper', async () => {
      await storage.addPriceHistory(scraperId, '99.99');
      await storage.addPriceHistory(scraperId, '89.99');

      const history = await storage.getPriceHistory(scraperId);
      expect(history).toHaveLength(2);
      // Should be sorted by timestamp descending (newest first)
      expect(history[0].price).toBe('89.99');
      expect(history[1].price).toBe('99.99');
    });

    it('should return empty array for scraper with no price history', async () => {
      const history = await storage.getPriceHistory(scraperId);
      expect(history).toHaveLength(0);
    });

    it('should update average price', async () => {
      await storage.updateAveragePrice(scraperId);

      const scraper = await storage.getScraper(scraperId);
      expect(scraper?.averagePrice).toBe('100.00');
    });

    it('should delete price history when scraper is deleted', async () => {
      await storage.addPriceHistory(scraperId, '99.99');
      await storage.addPriceHistory(scraperId, '89.99');

      await storage.deleteScraper(scraperId);

      const history = await storage.getPriceHistory(scraperId);
      expect(history).toHaveLength(0);
    });
  });
});
