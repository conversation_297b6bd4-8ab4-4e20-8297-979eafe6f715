# PricePulse Test Suite

This directory contains comprehensive unit tests for the PricePulse application using Vitest and React Testing Library.

## Test Structure

```
tests/
├── setup.ts                    # Test setup and configuration
├── vitest.d.ts                 # TypeScript declarations for Vitest
├── adapters/                   # Tests for scraper adapters
│   └── adapter-registry.test.ts
├── api/                        # Tests for API routes
│   └── routes.test.ts
├── components/                 # Tests for React components
│   └── App.test.tsx
├── lib/                        # Tests for utility libraries
│   └── utils.test.ts
├── shared/                     # Tests for shared schemas
│   └── schema.test.ts
├── storage/                    # Tests for storage layer
│   └── storage.test.ts
└── utils/                      # Tests for utility functions
    └── price-calculator.test.ts
```

## Running Tests

### All Tests
```bash
npm test
```

### Watch Mode (for development)
```bash
npm test
```

### Run Tests Once
```bash
npm run test:run
```

### Test UI (Interactive)
```bash
npm run test:ui
```

### Coverage Report
```bash
npm run test:coverage
```

## Test Categories

### 1. Utility Functions
- **Price Parser**: Tests for parsing various international price formats
- **Price Calculator**: Tests for 90-day average calculations and price statistics
- **Utils**: Tests for className utility functions

### 2. Schema Validation
- **Insert Scraper Schema**: Validation tests for creating new scrapers
- **Update Scraper Schema**: Validation tests for updating existing scrapers

### 3. Storage Layer
- **CRUD Operations**: Tests for scraper and price history operations
- **Data Integrity**: Tests for data consistency and error handling

### 4. API Routes
- **REST Endpoints**: Tests for all API routes with proper error handling
- **Validation**: Tests for request validation and response formatting

### 5. React Components
- **App Component**: Tests for main application structure
- **Component Integration**: Tests for component rendering and interactions

### 6. Scraper Adapters
- **Adapter Registry**: Tests for adapter management and configuration
- **Interface Compliance**: Tests for adapter interface consistency

## Test Configuration

The test suite is configured with:

- **Vitest**: Fast unit test runner built on top of Vite
- **jsdom**: Browser environment simulation for React component testing
- **@testing-library/react**: Utilities for testing React components
- **@testing-library/jest-dom**: Additional matchers for DOM testing

## Writing New Tests

### Basic Test Structure
```typescript
import { describe, it, expect } from 'vitest';

describe('Feature Name', () => {
  it('should do something specific', () => {
    // Arrange
    const input = 'test input';
    
    // Act
    const result = functionUnderTest(input);
    
    // Assert
    expect(result).toBe('expected output');
  });
});
```

### React Component Tests
```typescript
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import Component from '../path/to/Component';

describe('Component', () => {
  it('should render correctly', () => {
    render(<Component />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

### Mocking Dependencies
```typescript
import { vi } from 'vitest';

// Mock a module
vi.mock('../path/to/module', () => ({
  functionName: vi.fn(),
}));

// Mock a function
const mockFunction = vi.fn();
mockFunction.mockReturnValue('mocked value');
```

## Best Practices

1. **Test Naming**: Use descriptive test names that explain what is being tested
2. **Arrange-Act-Assert**: Structure tests with clear setup, execution, and verification
3. **Isolation**: Each test should be independent and not rely on other tests
4. **Coverage**: Aim for high test coverage but focus on critical business logic
5. **Mocking**: Mock external dependencies to keep tests fast and reliable

## Continuous Integration

Tests are automatically run in CI/CD pipelines to ensure code quality and prevent regressions.

## Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all imports use the correct paths relative to the test file
2. **Mock Issues**: Ensure mocks are properly configured before importing the modules that use them
3. **Async Tests**: Use proper async/await patterns for testing asynchronous code

### Debug Mode
To debug tests, you can use:
```bash
npm test -- --reporter=verbose
```

For more detailed debugging, use the Vitest UI:
```bash
npm run test:ui
```
