import { describe, it, expect } from 'vitest';
import { calculate90DayAverage, calculatePriceStatistics } from '../../server/src/utils/price-calculator';
import type { PriceHistory } from '@shared/schema';

describe('Price Calculator', () => {
  const createPriceEntry = (price: string, daysAgo: number): PriceHistory => ({
    id: `test-${daysAgo}`,
    scraperId: 'test-scraper',
    price,
    timestamp: new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000),
  });

  describe('calculate90DayAverage', () => {
    it('should return null for empty price history', () => {
      expect(calculate90DayAverage([])).toBe(null);
      expect(calculate90DayAverage(null as any)).toBe(null);
      expect(calculate90DayAverage(undefined as any)).toBe(null);
    });

    it('should calculate average for prices within 90 days', () => {
      const priceHistory = [
        createPriceEntry('100.00', 1),
        createPriceEntry('95.00', 5),
        createPriceEntry('110.00', 10),
        createPriceEntry('105.00', 30),
        createPriceEntry('90.00', 60),
      ];

      const average = calculate90DayAverage(priceHistory);
      expect(average).toBe('100.00'); // (100 + 95 + 110 + 105 + 90) / 5 = 100
    });

    it('should exclude prices older than 90 days', () => {
      const priceHistory = [
        createPriceEntry('100.00', 1),
        createPriceEntry('95.00', 5),
        createPriceEntry('110.00', 10),
        createPriceEntry('200.00', 100), // Should be excluded
        createPriceEntry('300.00', 120), // Should be excluded
      ];

      const average = calculate90DayAverage(priceHistory);
      expect(average).toBe('101.67'); // (100 + 95 + 110) / 3 = 101.67
    });

    it('should handle invalid price values', () => {
      const priceHistory = [
        createPriceEntry('100.00', 1),
        createPriceEntry('invalid', 5),
        createPriceEntry('95.00', 10),
        createPriceEntry('', 15),
        createPriceEntry('110.00', 20),
      ];

      const average = calculate90DayAverage(priceHistory);
      expect(average).toBe('101.67'); // (100 + 95 + 110) / 3 = 101.67
    });

    it('should return null if no valid prices found', () => {
      const priceHistory = [
        createPriceEntry('invalid', 1),
        createPriceEntry('', 5),
        createPriceEntry('not-a-number', 10),
      ];

      const average = calculate90DayAverage(priceHistory);
      expect(average).toBe(null);
    });

    it('should handle single price entry', () => {
      const priceHistory = [createPriceEntry('99.99', 1)];
      const average = calculate90DayAverage(priceHistory);
      expect(average).toBe('99.99');
    });

    it('should handle decimal precision correctly', () => {
      const priceHistory = [
        createPriceEntry('99.99', 1),
        createPriceEntry('100.01', 2),
      ];

      const average = calculate90DayAverage(priceHistory);
      expect(average).toBe('100.00'); // (99.99 + 100.01) / 2 = 100.00
    });
  });

  describe('calculatePriceStatistics', () => {
    it('should return null values for empty price history', () => {
      const stats = calculatePriceStatistics([]);
      expect(stats).toEqual({
        currentPrice: null,
        lowestPrice: null,
        previousPrice: null,
        averagePrice: null,
      });
    });

    it('should calculate all price statistics correctly', () => {
      const priceHistory = [
        createPriceEntry('100.00', 0), // Today (current)
        createPriceEntry('95.00', 1),  // Yesterday (previous)
        createPriceEntry('110.00', 3),
        createPriceEntry('85.00', 4),  // Lowest
        createPriceEntry('105.00', 5),
      ];

      const stats = calculatePriceStatistics(priceHistory);
      expect(stats.currentPrice).toBe('100.00');
      expect(stats.previousPrice).toBe('95.00');
      expect(stats.lowestPrice).toBe('85.00');
      expect(stats.averagePrice).toBe('99.00'); // (100 + 95 + 110 + 85 + 105) / 5 = 99
    });

    it('should handle single price entry', () => {
      const priceHistory = [createPriceEntry('99.99', 0)]; // Today
      const stats = calculatePriceStatistics(priceHistory);

      expect(stats.currentPrice).toBe('99.99');
      expect(stats.previousPrice).toBe(null); // No previous day entry
      expect(stats.lowestPrice).toBe('99.99');
      expect(stats.averagePrice).toBe('99.99');
    });

    it('should handle two price entries', () => {
      const priceHistory = [
        createPriceEntry('100.00', 0), // Today
        createPriceEntry('95.00', 1),  // Yesterday
      ];

      const stats = calculatePriceStatistics(priceHistory);
      expect(stats.currentPrice).toBe('100.00');
      expect(stats.previousPrice).toBe('95.00');
      expect(stats.lowestPrice).toBe('95.00');
      expect(stats.averagePrice).toBe('97.50');
    });

    it('should ignore invalid price values', () => {
      const priceHistory = [
        createPriceEntry('100.00', 0), // Today
        createPriceEntry('invalid', 1),
        createPriceEntry('95.00', 2),  // Previous valid day
        createPriceEntry('', 3),
        createPriceEntry('85.00', 4),
      ];

      const stats = calculatePriceStatistics(priceHistory);
      expect(stats.currentPrice).toBe('100.00');
      expect(stats.previousPrice).toBe('95.00');
      expect(stats.lowestPrice).toBe('85.00');
      expect(stats.averagePrice).toBe('93.33'); // (100 + 95 + 85) / 3 = 93.33
    });
  });
});
