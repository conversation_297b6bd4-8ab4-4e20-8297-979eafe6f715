import { describe, it, expect } from 'vitest';
import { insertScraperSchema, updateScraperSchema } from '../../shared/schema';

describe('Schema Validation', () => {
  describe('insertScraperSchema', () => {
    it('should validate valid scraper data', () => {
      const validData = {
        itemName: 'Test Product',
        url: 'https://example.com/product',
        selector: '.price',
      };

      const result = insertScraperSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should reject empty item name', () => {
      const invalidData = {
        itemName: '',
        url: 'https://example.com/product',
        selector: '.price',
      };

      const result = insertScraperSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Item name is required');
      }
    });

    it('should reject invalid URL', () => {
      const invalidData = {
        itemName: 'Test Product',
        url: 'not-a-valid-url',
        selector: '.price',
      };

      const result = insertScraperSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Must be a valid URL');
      }
    });

    it('should reject empty selector', () => {
      const invalidData = {
        itemName: 'Test Product',
        url: 'https://example.com/product',
        selector: '',
      };

      const result = insertScraperSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('CSS selector is required');
      }
    });

    it('should reject missing required fields', () => {
      const invalidData = {
        itemName: 'Test Product',
        // Missing url and selector
      };

      const result = insertScraperSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.length).toBeGreaterThan(0);
      }
    });

    it('should accept various valid URL formats', () => {
      const validUrls = [
        'https://example.com',
        'http://example.com',
        'https://subdomain.example.com/path',
        'https://example.com:8080/path?query=value',
      ];

      validUrls.forEach(url => {
        const data = {
          itemName: 'Test Product',
          url,
          selector: '.price',
        };

        const result = insertScraperSchema.safeParse(data);
        expect(result.success).toBe(true);
      });
    });
  });

  describe('updateScraperSchema', () => {
    it('should validate partial updates', () => {
      const validData = {
        itemName: 'Updated Product Name',
      };

      const result = updateScraperSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should validate all optional fields', () => {
      const validData = {
        itemName: 'Updated Product',
        url: 'https://updated-example.com',
        selector: '.new-price',
        currentPrice: '99.99',
        lowestPrice: '89.99',
        averagePrice: '94.99',
        status: 'active' as const,
        lastError: null,
      };

      const result = updateScraperSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should accept null values for nullable fields', () => {
      const validData = {
        currentPrice: null,
        lowestPrice: null,
        averagePrice: null,
        lastError: null,
      };

      const result = updateScraperSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should validate status enum values', () => {
      const validStatuses = ['active', 'updating', 'error'];
      
      validStatuses.forEach(status => {
        const data = { status };
        const result = updateScraperSchema.safeParse(data);
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid status values', () => {
      const invalidData = {
        status: 'invalid-status',
      };

      const result = updateScraperSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid URL in updates', () => {
      const invalidData = {
        url: 'not-a-valid-url',
      };

      const result = updateScraperSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject empty strings for required fields', () => {
      const invalidData = {
        itemName: '',
        selector: '',
      };

      const result = updateScraperSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should accept empty object (no updates)', () => {
      const result = updateScraperSchema.safeParse({});
      expect(result.success).toBe(true);
    });
  });
});
